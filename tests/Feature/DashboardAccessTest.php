<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('management user can access management dashboard', function () {
    $estate = \App\Models\Estate::factory()->create();
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $user->assignedEstates()->attach($estate, ['assigned_by' => $user->id]);
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(200);
});

test('reviewer user can access reviewer dashboard', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(200);
});

test('caretaker user can access caretaker dashboard', function () {
    $estate = \App\Models\Estate::factory()->create();
    $user = User::factory()->create(['role' => UserRole::CARETAKER]);
    $user->assignedEstates()->attach($estate, ['assigned_by' => $user->id]);

    $this->actingAs($user);

    $response = $this->get(route('caretaker.dashboard'));

    if ($response->status() !== 200) {
        echo 'Response status: '.$response->status()."\n";
        echo 'Response content: '.$response->content()."\n";
    }

    $response->assertStatus(200);
});

test('management user cannot access reviewer dashboard', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $response = $this->get(route('reviewer.dashboard'));

    $response->assertStatus(403);
});

test('reviewer user cannot access management dashboard', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($user);

    $response = $this->get(route('management.dashboard'));

    $response->assertStatus(403);
});
