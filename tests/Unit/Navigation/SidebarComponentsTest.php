<?php

use App\Enums\UserRole;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('admin sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('System Overview');
    $view->assertSee('User Management');
    $view->assertSee('System Settings');
    $view->assertSee('Estate Assignments');
    $view->assertSee('Team Management');
    $view->assertSee('Audit Logs');
});

test('manager sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-manager-sidebar title="Management Dashboard" />',
        ['title' => 'Management Dashboard']
    );

    $view->assertSee('Management Dashboard');
    $view->assertSee('Estates');
    $view->assertSee('Houses');
    $view->assertSee('Contacts');
    $view->assertSee('Team Management');
    $view->assertSee('Reports');
});

test('reviewer sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-reviewer-sidebar title="Reviewer Dashboard" />',
        ['title' => 'Reviewer Dashboard']
    );

    $view->assertSee('Reviewer Dashboard');
    $view->assertSee('Billing');
    $view->assertSee('Readings');
    $view->assertSee('Reports');
});

test('caretaker sidebar component renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::CARETAKER]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-caretaker-sidebar title="Caretaker Dashboard" />',
        ['title' => 'Caretaker Dashboard']
    );

    $view->assertSee('Caretaker Dashboard');
    $view->assertSee('Estates');
    $view->assertSee('Houses');
    $view->assertSee('Readings');
    $view->assertSee('Contacts');
});

test('resident portal layout renders correctly', function () {
    $user = User::factory()->create(['role' => UserRole::RESIDENT]);
    $this->actingAs($user);

    $response = $this->get(route('resident.dashboard'));

    $response->assertSee('Resident Portal');
    $response->assertSee('Dashboard');
    $response->assertSee('Invoices');
    $response->assertSee('Readings');
    $response->assertSee('Messages');
    $response->assertSee('Contact Us');
});

test('admin sidebar includes user profile section', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN, 'name' => 'John Doe', 'email' => '<EMAIL>']);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('John Doe');
    $view->assertSee('<EMAIL>');
    $view->assertSee('JD');
    // First letters of name
});

test('manager sidebar includes user profile section', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER, 'name' => 'Jane Smith', 'email' => '<EMAIL>']);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-manager-sidebar title="Management Dashboard" />',
        ['title' => 'Management Dashboard']
    );

    $view->assertSee('Jane Smith');
    $view->assertSee('<EMAIL>');
    $view->assertSee('JS');
    // First letters of name
});

test('sidebar components have proper structure', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('sidebar');
    $view->assertSee('navigation');
    $view->assertSee('flex');
    $view->assertSee('h-screen');
});

test('sidebar components have dark mode support', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('dark:bg-gray-900');
    $view->assertSee('dark:border-gray-800');
    $view->assertSee('dark:text-white');
});

test('sidebar components have responsive design', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('lg:static');
    $view->assertSee('lg:translate-x-0');
    $view->assertSee('w-72');
});

test('sidebar components display logo', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('Water');
    $view->assertSee('MS');
    $view->assertSee('text-blue-600');
});

test('sidebar components have collapsible logo', function () {
    $user = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($user);

    $view = $this->blade(
        '<x-admin-sidebar title="Admin Dashboard" />',
        ['title' => 'Admin Dashboard']
    );

    $view->assertSee('W');
    // Collapsed logo
    $view->assertSee('hidden');
    // Classes for hiding/showing elements
});
