<?php

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Support\Facades\Gate;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

it('registers all permissions from user roles', function () {
    // Create a user with management role
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->actingAs($user);

    // Get all unique permissions from all roles
    $allPermissions = collect(UserRole::cases())
        ->flatMap(fn ($role) => $role->permissions())
        ->unique()
        ->values()
        ->all();

    // Check that each permission is registered as a gate
    foreach ($allPermissions as $permission) {
        expect(Gate::has($permission))->toBeTrue("Gate for permission '{$permission}' is not registered");

        // Check that the gate works correctly for a user with the permission
        if (in_array($permission, UserRole::MANAGER->permissions())) {
            expect(Gate::allows($permission))->toBeTrue("Gate for permission '{$permission}' does not allow a user with the permission");
        }
    }

    // Create a user with viewer role (which has limited permissions)
    $viewerUser = User::factory()->create(['role' => UserRole::ADMIN]);
    $this->actingAs($viewerUser);

    // Check that permissions not assigned to viewer are denied
    $viewerPermissions = UserRole::ADMIN->permissions();
    $nonViewerPermissions = array_diff($allPermissions, $viewerPermissions);

    foreach ($nonViewerPermissions as $permission) {
        expect(Gate::allows($permission))->toBeFalse("Gate for permission '{$permission}' incorrectly allows a user without the permission");
    }
});
