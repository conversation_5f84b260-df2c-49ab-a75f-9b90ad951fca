<?php

namespace App\Services;

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Support\Facades\Cache;

class PermissionValidationService
{
    /**
     * Validate if user has a specific permission with optional estate scoping
     */
    public function validateUserPermission(User $user, string $permission, ?Estate $estate = null): bool
    {
        // 1. Check user overrides first (highest priority)
        if ($override = $this->getUserOverride($user, $permission)) {
            return $this->evaluateOverride($override);
        }

        // 2. Check role-based permissions
        if (! $user->hasPermission($permission)) {
            return false;
        }

        // 3. Validate estate assignment if required
        if ($this->permissionRequiresEstate($permission)) {
            return $this->validateEstateAccess($user, $estate);
        }

        return true;
    }

    /**
     * Get user's effective permissions (including overrides)
     */
    public function getUserPermissions(User $user): array
    {
        $cacheKey = "user_permissions_{$user->id}";

        return Cache::remember($cacheKey, now()->addHours(1), function () use ($user) {
            $permissions = $user->role->permissions();

            // Apply user overrides
            foreach ($user->permissionOverrides as $override) {
                if ($override->pivot->expires_at && $override->pivot->expires_at->isPast()) {
                    continue;
                }

                if ($override->pivot->action === 'grant') {
                    $permissions[] = $override->name;
                } else {
                    $permissions = array_diff($permissions, [$override->name]);
                }
            }

            return array_unique($permissions);
        });
    }

    /**
     * Check if user can access a specific estate
     */
    public function canAccessEstate(User $user, Estate $estate): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can access a specific house
     */
    public function canAccessHouse(User $user, $house): bool
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        if ($user->hasRole(UserRole::RESIDENT)) {
            return $user->contacts()->where('house_id', $house->id)->exists();
        }

        return $user->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }

    /**
     * Get user's accessible estates
     */
    public function getAccessibleEstates(User $user)
    {
        if ($user->hasRole(UserRole::ADMIN)) {
            return \App\Models\Estate::pluck('name', 'id');
        }

        return $user->assignedEstates()->with('houses')->pluck('name', 'id');
    }

    /**
     * Invalidate user's permission cache
     */
    public function invalidateUserCache(User $user): void
    {
        Cache::forget("user_permissions_{$user->id}");
    }

    /**
     * Invalidate role cache for all users with a specific role
     */
    public function invalidateRoleCache(UserRole $role): void
    {
        $users = User::where('role', $role->value)->get();
        foreach ($users as $user) {
            $this->invalidateUserCache($user);
        }
    }

    /**
     * Get user override for a specific permission
     */
    private function getUserOverride(User $user, string $permission): ?Permission
    {
        return $user->permissionOverrides()
            ->where('name', $permission)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->first();
    }

    /**
     * Evaluate permission override
     */
    private function evaluateOverride(Permission $override): bool
    {
        return $override->pivot->action === 'grant';
    }

    /**
     * Check if permission requires estate assignment
     */
    private function permissionRequiresEstate(string $permission): bool
    {
        $permissionModel = Permission::where('name', $permission)->first();

        return $permissionModel ? $permissionModel->requires_estate_assignment : false;
    }

    /**
     * Validate estate access for user
     */
    private function validateEstateAccess(User $user, ?Estate $estate): bool
    {
        if (! $estate) {
            return false;
        }

        // Admin users have access to all estates
        if ($user->hasRole(UserRole::ADMIN)) {
            return true;
        }

        return $user->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Check if user can manage another user
     */
    public function canManageUser(User $manager, User $subordinate): bool
    {
        // Admin can manage everyone
        if ($manager->hasRole(UserRole::ADMIN)) {
            return true;
        }

        // Manager can manage reviewers and caretakers
        if ($manager->hasRole(UserRole::MANAGER)) {
            return $subordinate->hasRole(UserRole::REVIEWER) ||
                   $subordinate->hasRole(UserRole::CARETAKER);
        }

        // Reviewer can manage caretakers
        if ($manager->hasRole(UserRole::REVIEWER)) {
            return $subordinate->hasRole(UserRole::CARETAKER);
        }

        return false;
    }
}
