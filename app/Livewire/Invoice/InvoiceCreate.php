<?php

namespace App\Livewire\Invoice;

use App\Models\House;
use App\Models\Invoice;
use App\Models\WaterRate;
use App\Services\InvoiceGenerationService;
use Livewire\Component;
use Livewire\WithFileUploads;

class InvoiceCreate extends Component
{
    use WithFileUploads;

    public $house_id;

    public $period_start;

    public $period_end;

    public $current_reading;

    public $previous_reading;

    public $consumption;

    public $amount;

    public $notes;

    public $houses = [];

    public $waterRates = [];

    protected function rules()
    {
        return [
            'house_id' => 'required|exists:houses,id',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after:period_start',
            'current_reading' => 'required|numeric|min:0',
            'previous_reading' => 'required|numeric|min:0',
            'consumption' => 'required|numeric|min:0',
            'amount' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ];
    }

    public function mount()
    {
        $this->houses = House::with('estate')->get();
        $this->waterRates = WaterRate::active()->get();
        $this->period_start = now()->startOfMonth()->format('Y-m-d');
        $this->period_end = now()->endOfMonth()->format('Y-m-d');
    }

    public function updated($propertyName)
    {
        if (in_array($propertyName, ['current_reading', 'previous_reading'])) {
            $this->calculateConsumption();
        }
    }

    public function calculateConsumption()
    {
        if ($this->current_reading && $this->previous_reading) {
            $this->consumption = max(0, $this->current_reading - $this->previous_reading);
            $this->calculateAmount();
        }
    }

    public function calculateAmount()
    {
        if ($this->consumption > 0) {
            $service = new InvoiceGenerationService;
            $this->amount = $service->calculateAmount($this->consumption);
        }
    }

    public function save()
    {
        $this->validate();

        $invoice = Invoice::create([
            'house_id' => $this->house_id,
            'period_start' => $this->period_start,
            'period_end' => $this->period_end,
            'current_reading' => $this->current_reading,
            'previous_reading' => $this->previous_reading,
            'consumption' => $this->consumption,
            'amount' => $this->amount,
            'notes' => $this->notes,
            'status' => 'pending',
        ]);

        session()->flash('message', 'Invoice created successfully.');

        return redirect()->route('invoices.show', $invoice);
    }

    public function render()
    {
        return view('livewire.invoice.invoice-create')
            ->layout('layouts.app');
    }
}
