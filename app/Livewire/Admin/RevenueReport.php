<?php

namespace App\Livewire\Admin;

use App\Exports\RevenueReportExport;
use App\Services\FinancialReportService;
use App\Services\RevenueReportPdfService;
use Carbon\Carbon;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class RevenueReport extends Component
{
    public $date_from;

    public $date_to;

    public $estate_id = null;

    public $revenue_report = [];

    public $show_report = false;

    public function mount()
    {
        $this->authorize('reports.view_all');
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->endOfMonth()->format('Y-m-d');
        $this->generateReport();
    }

    public function generateReport()
    {
        $this->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'estate_id' => 'nullable|exists:estates,id',
        ]);

        $startDate = Carbon::parse($this->date_from);
        $endDate = Carbon::parse($this->date_to);

        $service = new FinancialReportService;
        $this->revenue_report = $service->generateRevenueReport($startDate, $endDate, $this->estate_id);
        $this->show_report = true;
    }

    public function exportToExcel()
    {
        $this->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'estate_id' => 'nullable|exists:estates,id',
        ]);

        $startDate = Carbon::parse($this->date_from);
        $endDate = Carbon::parse($this->date_to);

        $service = new FinancialReportService;
        $revenueReport = $service->generateRevenueReport($startDate, $endDate, $this->estate_id);

        return Excel::download(new RevenueReportExport($revenueReport), 'revenue-report-'.now()->format('Y-m-d').'.xlsx');
    }

    public function exportToPdf()
    {
        $this->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'estate_id' => 'nullable|exists:estates,id',
        ]);

        $startDate = Carbon::parse($this->date_from);
        $endDate = Carbon::parse($this->date_to);

        $service = new FinancialReportService;
        $revenueReport = $service->generateRevenueReport($startDate, $endDate, $this->estate_id);

        $pdfService = new RevenueReportPdfService;
        $pdfPath = $pdfService->generateRevenueReportPdf($revenueReport);

        return response()->download(storage_path('app/'.$pdfPath), 'revenue-report-'.now()->format('Y-m-d').'.pdf');
    }

    public function render()
    {
        return view('livewire.admin.revenue-report')
            ->layout('layouts.app');
    }
}
