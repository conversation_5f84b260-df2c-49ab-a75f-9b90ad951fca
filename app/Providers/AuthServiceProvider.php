<?php

namespace App\Providers;

use App\Enums\UserRole;
use App\Models\Invoice;
use App\Policies\InvoicePolicy;
use App\Services\PermissionValidationService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Invoice::class => InvoicePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Register all permissions from UserRole enum
        $this->registerAllPermissions();

        // Register estate-aware permission gates
        $this->registerEstateAwarePermissions();

        // Register role-based gates
        $this->registerRoleGates();

        // Register estate-based gates
        $this->registerEstateGates();
    }

    /**
     * Register all permissions from UserRole enum
     */
    private function registerAllPermissions(): void
    {
        // Get all unique permissions from all roles
        $allPermissions = collect(UserRole::cases())
            ->flatMap(fn ($role) => $role->permissions())
            ->unique()
            ->values()
            ->all();

        // Register each permission as a gate
        foreach ($allPermissions as $permission) {
            Gate::define($permission, function ($user) use ($permission) {
                return $user->hasPermission($permission);
            });
        }
    }

    /**
     * Register role-based gates
     */
    private function registerRoleGates(): void
    {
        Gate::define('is-admin', function ($user) {
            return $user->hasRole(UserRole::ADMIN);
        });

        Gate::define('is-manager', function ($user) {
            return $user->hasRole(UserRole::MANAGER);
        });

        Gate::define('is-reviewer', function ($user) {
            return $user->hasRole(UserRole::REVIEWER);
        });

        Gate::define('is-caretaker', function ($user) {
            return $user->hasRole(UserRole::CARETAKER);
        });

        Gate::define('is-resident', function ($user) {
            return $user->hasRole(UserRole::RESIDENT);
        });

        // Management hierarchy gates
        Gate::define('manage-users', function ($user, $targetUser = null) {
            if (! $targetUser) {
                return $user->hasPermission('users.assign_roles');
            }

            return app(PermissionValidationService::class)->canManageUser($user, $targetUser);
        });

        Gate::define('assign-estates', function ($user) {
            return $user->hasPermission('users.assign_estates');
        });
    }

    /**
     * Register estate-based gates
     */
    private function registerEstateGates(): void
    {
        Gate::define('access-estate', function ($user, $estate) {
            return app(PermissionValidationService::class)->canAccessEstate($user, $estate);
        });

        Gate::define('access-house', function ($user, $house) {
            return app(PermissionValidationService::class)->canAccessHouse($user, $house);
        });

        Gate::define('manage-estate', function ($user, $estate) {
            if (! app(PermissionValidationService::class)->canAccessEstate($user, $estate)) {
                return false;
            }

            return $user->hasPermission('estates.manage_assigned') || $user->hasPermission('estates.manage_all');
        });

        Gate::define('view-estate-data', function ($user, $estate) {
            if (! app(PermissionValidationService::class)->canAccessEstate($user, $estate)) {
                return false;
            }

            return $user->hasPermission('estates.view_assigned') || $user->hasPermission('estates.view_all');
        });
    }

    /**
     * Register estate-aware permission gates
     */
    private function registerEstateAwarePermissions(): void
    {
        // Define permissions that require estate assignment
        // These are permissions with 'assigned' in the name that require estate access
        $estateAwarePermissions = [
            'estates.view_assigned',
            'estates.manage_assigned',
            'estates.edit_assigned',
            'houses.view_assigned',
            'houses.manage_assigned',
            'houses.edit_assigned',
            'contacts.view_assigned',
            'contacts.manage_assigned',
            'readings.view_assigned',
            'readings.create_assigned',
            'readings.edit_assigned',
            'readings.review_assigned',
            'readings.approve_assigned',
            'invoices.view_assigned',
            'invoices.generate_assigned',
            'invoices.edit_assigned',
            'invoices.send_assigned',
            'invoices.adjust_assigned',
            'invoices.export_assigned',
            'invoices.approve_assigned',
            'invoices.delete_assigned',
            'rates.view_assigned',
            'rates.edit_assigned',
            'reports.view_assigned',
            'reports.generate_assigned',
            'analytics.view_assigned',
            'export.data_assigned',
            'users.view_assigned',
            'users.create_assigned',
            'users.edit_assigned',
            'whatsapp.send_assigned',
            'whatsapp.send_invoices_assigned',
            'accounts.view_assigned',
            'accounts.manage_assigned',
            'accounts.view_balance_assigned',
            'accounts.view_transactions_assigned',
            'accounts.create_transaction_assigned',
            'accounts.edit_transaction_assigned',
            'accounts.view_statement_assigned',
            'accounts.export_statement_assigned',
            'accounts.adjust_balance_assigned',
            'payments.view_assigned',
            'payments.approve_assigned',
            'payments.create_assigned',
            'payments.edit_assigned',
            'payments.export_assigned',
            'payments.reconcile_assigned',
            'reports.aging_assigned',
            'reports.revenue_assigned',
            'reports.billing_assigned',
            'reports.customer_statements_assigned',
            'reports.financial_assigned',
            'reports.balance_list_assigned',
            'invoices.view_status_assigned',
            'resident.inquiries.view_assigned',
        ];

        // Register gates for estate-aware permissions
        foreach ($estateAwarePermissions as $permission) {
            Gate::define($permission, function ($user) use ($permission) {
                // Check if user has the basic permission first
                if (! $user->hasPermission($permission)) {
                    return false;
                }

                // Check if user has assigned estates
                return $user->assignedEstates()->exists();
            });
        }
    }
}
